package com.touptek.measurerealize.utils

import android.graphics.*
import android.util.Log
import android.view.MotionEvent
import android.widget.ImageView
import com.touptek.measurerealize.TpImageView
import java.util.*
import kotlin.math.*

/**
 * 🎯 中心圆测量实例 - 专业级数据结构
 */
data class CenterCircleMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [圆心, 半径控制点] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [圆心, 半径控制点] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis(),
    // 缓存计算结果以提高性能
    private var cachedRadius: Double? = null,
    private var cachedArea: Double? = null,
    private var cachedPerimeter: Double? = null,
    private var cacheValidTime: Long = 0L
) {
    companion object {
        private const val CACHE_VALIDITY_MS = 100L // 缓存有效期100ms
    }

    /**
     * 🎯 计算圆的半径（使用位图坐标 - 真实半径，不受缩放影响）
     */
    fun calculateRadius(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedRadius != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedRadius!!
        }

        if (bitmapPoints.size < 2) return 0.0

        val center = bitmapPoints[0]
        val radiusPoint = bitmapPoints[1]
        val dx = radiusPoint.x - center.x
        val dy = radiusPoint.y - center.y
        val radius = sqrt((dx * dx + dy * dy).toDouble())

        // 更新缓存
        cachedRadius = radius
        cacheValidTime = currentTime
        return radius
    }

    /**
     * 🎯 计算圆的视图半径（用于绘制，确保与控制点一致）
     */
    fun calculateViewRadius(): Float {
        if (viewPoints.size < 2) return 0f

        val center = viewPoints[0]
        val radiusPoint = viewPoints[1]
        val dx = radiusPoint.x - center.x
        val dy = radiusPoint.y - center.y
        return sqrt(dx * dx + dy * dy)
    }

    /**
     * 🎯 计算圆的面积（使用位图坐标）
     */
    fun calculateArea(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedArea != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedArea!!
        }

        val radius = calculateRadius()
        val area = PI * radius * radius

        cachedArea = area
        return area
    }

    /**
     * 🎯 计算圆的周长（使用位图坐标）
     */
    fun calculatePerimeter(): Double {
        val currentTime = System.currentTimeMillis()
        if (cachedPerimeter != null && (currentTime - cacheValidTime) < CACHE_VALIDITY_MS) {
            return cachedPerimeter!!
        }

        val radius = calculateRadius()
        val perimeter = 2.0 * PI * radius

        cachedPerimeter = perimeter
        return perimeter
    }

    /**
     * 🎯 检查点是否在触摸范围内 - 直接在视图坐标系中检测
     */
    fun isPointInTouchRange(touchPoint: PointF, touchRadius: Float): Boolean {
        return viewPoints.any { point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            distance <= touchRadius
        }
    }

    /**
     * 🎯 检查特定点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        if (pointIndex < 0 || pointIndex >= viewPoints.size) return false
        val point = viewPoints[pointIndex]
        val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
        return distance <= touchRadius
    }

    /**
     * ⭕ 计算圆周长
     */
    fun calculateCircumference(): Double {
        return calculatePerimeter() // 圆周长就是周长
    }

    /**
     * ⭕ 计算文本位置
     */
    fun calculateTextPosition(): PointF {
        if (viewPoints.size < 2) return PointF(0f, 0f)
        val center = viewPoints[0]
        val radius = sqrt((viewPoints[1].x - center.x).pow(2) + (viewPoints[1].y - center.y).pow(2))
        // 文本位置在圆心下方
        return PointF(center.x, center.y + radius * 0.3f)
    }

    /**
     * 🎯 获取最近的控制点索引
     */
    fun getNearestPointIndex(touchPoint: PointF): Int {
        if (viewPoints.isEmpty()) return -1
        
        var nearestIndex = 0
        var minDistance = Float.MAX_VALUE
        
        viewPoints.forEachIndexed { index, point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }
        
        return nearestIndex
    }

    /**
     * 🎯 圆形约束更新方法 - 核心约束逻辑
     */
    fun updateWithCircleConstraint(pointIndex: Int, newPoint: PointF) {
        when (pointIndex) {
            0 -> { // 圆心移动 - 整个圆平移
                val deltaX = newPoint.x - viewPoints[0].x
                val deltaY = newPoint.y - viewPoints[0].y
                viewPoints[0] = newPoint
                if (viewPoints.size > 1) {
                    viewPoints[1] = PointF(viewPoints[1].x + deltaX, viewPoints[1].y + deltaY)
                }
            }
            1 -> { // 半径控制点移动 - 确保点在圆边缘上
                if (viewPoints.size >= 2) {
                    val center = viewPoints[0]

                    // 计算从圆心到新点的向量
                    val vectorX = newPoint.x - center.x
                    val vectorY = newPoint.y - center.y

                    // 计算新的半径（距离）
                    val newRadius = sqrt(vectorX * vectorX + vectorY * vectorY)

                    // 如果半径太小，设置最小半径
                    val minRadius = 20f
                    val actualRadius = if (newRadius < minRadius) minRadius else newRadius

                    // 归一化方向向量
                    val normalizedX = if (newRadius > 0) vectorX / newRadius else 1f
                    val normalizedY = if (newRadius > 0) vectorY / newRadius else 0f

                    // 计算在圆边缘上的新点位置
                    val constrainedPoint = PointF(
                        center.x + normalizedX * actualRadius,
                        center.y + normalizedY * actualRadius
                    )

                    viewPoints[1] = constrainedPoint
                }
            }
        }
        markAsModified()
        invalidateCache()
    }

    /**
     * 🔄 同步位图坐标（当需要保存时调用）
     */
    fun syncBitmapCoords(imageView: ImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
        invalidateCache()
    }

    /**
     * 🔄 同步视图坐标（缩放时调用）
     */
    fun syncViewCoords(imageView: ImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
            viewPoints.add(viewPoint)
        }
        invalidateCache()
    }

    /**
     * 🔄 坐标转换：视图坐标 → 位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()
        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }

    /**
     * 🔄 坐标转换：位图坐标 → 视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }

    /**
     * 更新最后修改时间
     */
    private fun markAsModified() {
        lastModified = System.currentTimeMillis()
    }

    /**
     * 清除缓存
     */
    private fun invalidateCache() {
        cachedRadius = null
        cachedArea = null
        cachedPerimeter = null
        cacheValidTime = 0L
    }

    /**
     * 获取圆形的显示文本
     */
    fun getDisplayText(): String {
        val radius = calculateRadius()
        val area = calculateArea()
        val perimeter = calculatePerimeter()
        return String.format("半径: %.1f px | 面积: %.1f px² | 周长: %.1f px", radius, area, perimeter)
    }
}

/**
 * 🎯 中心圆测量助手 - 专业级圆形测量系统
 */
class CenterCircleMeasureHelper {
    companion object {
        private const val TAG = "CenterCircleMeasureHelper"
        private const val TOUCH_RADIUS = 80f // 触摸检测半径
    }

    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var originalBitmap: Bitmap
    private var isInitialized = false

    // 测量数据管理
    private val measurements = mutableListOf<CenterCircleMeasurement>()
    private var selectedMeasurement: CenterCircleMeasurement? = null
    private var activeMeasurement: CenterCircleMeasurement? = null

    // 交互状态
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 回调
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🎯 初始化助手
     */
    fun init(imageView: TpImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.originalBitmap = bitmap
        this.isInitialized = true
        Log.d(TAG, "🎯 CenterCircleMeasureHelper initialized with bitmap: ${bitmap.width}x${bitmap.height}")
    }

    /**
     * 🎯 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🎯 开始新的圆形测量 - 在屏幕中央创建圆
     */
    fun startNewMeasurement(): String {
        // 在视图中心生成圆形
        val viewCenterX = imageView.width / 2f
        val viewCenterY = imageView.height / 2f
        val circleRadius = minOf(imageView.width, imageView.height) / 8f // 适中的圆形大小

        // 在视图坐标系中创建圆形的圆心和半径点
        val centerPoint = PointF(viewCenterX, viewCenterY)
        val radiusPoint = PointF(viewCenterX + circleRadius, viewCenterY)

        // 创建完整的测量
        val measurement = CenterCircleMeasurement(
            isSelected = true,
            isEditing = false,  // 直接设为完成状态，可拖动
            isCompleted = true
        )

        // 添加视图坐标点（圆心、半径点）
        measurement.viewPoints.addAll(listOf(centerPoint, radiusPoint))

        // 🐛 调试：输出初始视图坐标
        Log.d(TAG, "🐛 Initial view coords - Center: ($viewCenterX, $viewCenterY), Radius: (${viewCenterX + circleRadius}, $viewCenterY), Radius: $circleRadius")

        // 同步位图坐标
        measurement.syncBitmapCoords(imageView)

        // 🐛 调试：输出转换后的位图坐标
        if (measurement.bitmapPoints.size >= 2) {
            Log.d(TAG, "🐛 Converted bitmap coords - Center: (${measurement.bitmapPoints[0].x}, ${measurement.bitmapPoints[0].y}), Edge: (${measurement.bitmapPoints[1].x}, ${measurement.bitmapPoints[1].y})")
        }

        // 设置文本位置
        measurement.textPosition = PointF(viewCenterX, viewCenterY)

        // 取消其他测量的选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        measurements.add(measurement)
        activeMeasurement = null
        selectedMeasurement = measurement

        notifyUpdate()
        Log.d(TAG, "🎯 Created new circle measurement with radius: ${String.format("%.1f", measurement.calculateRadius())}px")
        return measurement.id
    }

    /**
     * 🎯 获取所有测量数据用于覆盖层显示
     */
    fun getAllMeasurementData(): List<CenterCircleMeasurement> {
        return measurements.toList()
    }

    /**
     * 🎯 获取当前选中的测量数据（向后兼容）
     */
    fun getMeasurementData(): CircleMeasurementData? {
        return selectedMeasurement?.let { measurement ->
            if (measurement.viewPoints.size >= 2) {
                CircleMeasurementData(
                    points = measurement.viewPoints.toList(),
                    radius = measurement.calculateRadius(),
                    area = measurement.calculateArea(),
                    perimeter = measurement.calculatePerimeter(),
                    isDragging = isDraggingPoint
                )
            } else null
        }
    }

    /**
     * 🎯 处理触摸事件 - 标准模式实现
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Helper not initialized, ignoring touch event")
            return false
        }

        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                Log.d(TAG, "🎯 ACTION_DOWN at ($x, $y)")

                // 检查控制点触摸（优先级：圆心 > 半径点）
                for (measurement in measurements.reversed()) {
                    if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                        val nearestPointIndex = measurement.getNearestPointIndex(touchPoint)
                        if (nearestPointIndex >= 0) {
                            selectedMeasurement = measurement
                            draggedPointIndex = nearestPointIndex
                            isDraggingPoint = true

                            measurements.forEach { it.isSelected = false }
                            measurement.isSelected = true

                            val pointName = when(nearestPointIndex) {
                                0 -> "center"
                                1 -> "radius point"
                                else -> "unknown"
                            }
                            Log.d(TAG, "🎯 Started dragging $pointName (index $nearestPointIndex) of measurement ${measurement.id}")
                            notifyUpdate()
                            return true
                        }
                    }
                }

                // 空白区域点击处理
                if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
                    measurements.forEach { it.isSelected = false }
                    selectedMeasurement = null
                    Log.d(TAG, "🎯 Clicked in empty area, cleared selection")
                    notifyUpdate()
                }

                return false
            }

            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(touchPoint)
            }

            MotionEvent.ACTION_UP -> {
                return handleTouchUp(touchPoint)
            }
        }

        return false
    }



    private fun handleTouchMove(touchPoint: PointF): Boolean {
        if (isDraggingPoint) {
            selectedMeasurement?.let { measurement ->
                if (draggedPointIndex < measurement.viewPoints.size) {
                    // 使用圆形约束更新点位置
                    measurement.updateWithCircleConstraint(draggedPointIndex, touchPoint)

                    // 同步位图坐标
                    measurement.syncBitmapCoords(imageView)

                    notifyUpdate()
                    return true
                }
            }
        }
        return false
    }

    private fun handleTouchUp(touchPoint: PointF): Boolean {
        if (isDraggingPoint) {
            isDraggingPoint = false
            draggedPointIndex = -1
            Log.d(TAG, "🎯 Finished dragging")
            notifyUpdate()
            return true
        }
        return false
    }



    /**
     * 🎯 选择测量
     */
    private fun selectMeasurement(measurement: CenterCircleMeasurement) {
        // 取消其他测量的选中状态
        measurements.forEach { it.isSelected = false }

        // 选中当前测量
        measurement.isSelected = true
        selectedMeasurement = measurement

        Log.d(TAG, "🎯 Selected measurement: ${measurement.id}")
    }

    /**
     * 🎯 通知更新
     */
    private fun notifyUpdate() {
        measurementUpdateCallback?.invoke()
    }

    /**
     * 🎯 暂停测量 - 保持数据但停止交互
     */
    fun pauseMeasurement() {
        // 保持所有数据，只是停止新的交互
        Log.d(TAG, "⏸️ Measurement paused - data preserved")
    }

    /**
     * 🎯 恢复测量 - 从暂停状态恢复到可编辑状态
     */
    fun resumeMeasurement() {
        // 恢复编辑状态，所有数据保持不变
        Log.d(TAG, "▶️ Measurement resumed - ready for continued editing")
    }

    /**
     * 🎯 缩放变化时同步坐标
     */
    fun onScaleChanged() {
        // 从位图坐标恢复视图坐标，确保测量跟随图像
        measurements.forEach { measurement ->
            measurement.syncViewCoords(imageView)
        }
        notifyUpdate()
        Log.d(TAG, "🔄 Synced coordinates after scale change")
    }

    /**
     * 🎯 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🎯 获取选中的测量
     */
    fun getSelectedMeasurement(): CenterCircleMeasurement? = selectedMeasurement

    /**
     * 🎯 删除选中的测量
     */
    fun deleteSelectedMeasurement(): Boolean {
        selectedMeasurement?.let { measurement ->
            measurements.remove(measurement)
            selectedMeasurement = null
            activeMeasurement = null
            notifyUpdate()
            Log.d(TAG, "🗑️ Deleted measurement: ${measurement.id}")
            return true
        }
        return false
    }

    /**
     * 🎯 清除所有测量
     */
    fun clearAllMeasurements() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        isDraggingPoint = false
        draggedPointIndex = -1
        notifyUpdate()
        Log.d(TAG, "🧹 Cleared all measurements")
    }

    /**
     * 🎯 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean = selectedMeasurement != null

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean = isDraggingPoint

    /**
     * ⭕ 清除所有选中状态
     */
    fun clearSelection() {
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        measurementUpdateCallback?.invoke()
    }

    /**
     * ⭕ 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            for (pointIndex in 0..1) { // 圆有两个点：中心点(0)和边缘点(1)
                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                    return true
                }
            }
            false
        }
    }

    /**
     * ⭕ 检查点是否在测量上（用于混合触摸处理器）
     */
    fun isPointOnMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            for (pointIndex in 0..1) { // 圆有两个点：中心点(0)和边缘点(1)
                if (measurement.isPointInTouchRange(touchPoint, pointIndex, TOUCH_RADIUS)) {
                    return true
                }
            }
            false
        }
    }

    /**
     * 🎯 判断触摸点是否在图像内容区域（避免UI区域误触）
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            // 出错时保守处理，不清除选中状态
            Log.e(TAG, "❌ Error checking image content area: ${e.message}")
            return false
        }
    }
}

package com.touptek.measurerealize.utils

import android.graphics.*
import android.util.Log
import android.view.MotionEvent
import com.touptek.measurerealize.TpImageView
import java.util.*
import kotlin.math.*

/**
 * 📏 平行线测量实例 - 专业级数据结构
 */
data class ParallelLinesMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [line1_start, line1_end, line2_start, line2_end] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [line1_start, line1_end, line2_start, line2_end] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,    // 距离文本位置
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis()
) {
    /**
     * 📏 计算平行线距离（使用位图坐标 - 真实距离，不受缩放影响）
     */
    fun calculateDistance(): Double {
        // 🔧 优先使用位图坐标计算真实距离，避免缩放影响
        val points = if (bitmapPoints.size >= 4) bitmapPoints else viewPoints
        if (points.size < 4) return 0.0

        val p1 = points[0] // 第一条线起点
        val p2 = points[1] // 第一条线终点
        val p3 = points[2] // 第二条线起点

        // 计算点到直线的距离（垂直距离）
        val A = p2.y - p1.y
        val B = p1.x - p2.x
        val C = p2.x * p1.y - p1.x * p2.y

        val distance = abs(A * p3.x + B * p3.y + C) / sqrt(A * A + B * B).toDouble()

        // 🔧 调试日志：记录使用的坐标系和计算结果
        val coordType = if (bitmapPoints.size >= 4) "bitmap" else "view"
        Log.d("ParallelLinesMeasureHelper", "🎯 Distance calculated using $coordType coordinates: $distance px")

        return distance
    }

    /**
     * 🎯 检查点是否在触摸范围内 - 直接在视图坐标系中检测
     */
    fun isPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        if (pointIndex >= viewPoints.size) return false
        val point = viewPoints[pointIndex]
        val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
        return distance <= touchRadius
    }

    /**
     * 🎯 检查触摸点是否在任何端点的触摸范围内 - 与LineMeasureHelper保持一致
     */
    fun isPointInTouchRange(touchPoint: PointF, touchRadius: Float): Boolean {
        return viewPoints.any { point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            distance <= touchRadius
        }
    }

    /**
     * 🎯 获取最近的点索引
     */
    fun getNearestPointIndex(touchPoint: PointF): Int {
        var nearestIndex = 0
        var minDistance = Float.MAX_VALUE

        viewPoints.forEachIndexed { index, point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }

        return nearestIndex
    }

    /**
     * 🔧 使用平行线约束更新点位置
     */
    fun updateWithParallelConstraint(pointIndex: Int, newPoint: PointF) {
        when (pointIndex) {
            0, 1 -> { // Line1的端点移动
                viewPoints[pointIndex] = newPoint
                updateParallelLine2() // 重新计算Line2的右端点保持平行
            }
            2 -> { // Line2左端点移动 - 整条Line2跟随移动
                val dx = newPoint.x - viewPoints[2].x
                val dy = newPoint.y - viewPoints[2].y
                viewPoints[2] = newPoint
                if (viewPoints.size > 3) {
                    viewPoints[3] = PointF(viewPoints[3].x + dx, viewPoints[3].y + dy)
                }
            }
            3 -> { // Line2右端点 - 约束到平行线上
                val projectedPoint = projectToParallelLine(newPoint)
                viewPoints[3] = projectedPoint
            }
        }
        markAsModified()
    }

    /**
     * 🔧 当Line1改变时，更新Line2保持平行
     */
    private fun updateParallelLine2() {
        if (viewPoints.size < 4) return

        val p1 = viewPoints[0] // Line1起点
        val p2 = viewPoints[1] // Line1终点
        val p3 = viewPoints[2] // Line2起点
        val oldP4 = viewPoints[3] // Line2终点

        // 计算Line1的方向向量
        val dirX = p2.x - p1.x
        val dirY = p2.y - p1.y

        // 计算原来Line2的长度
        val oldLength = sqrt((oldP4.x - p3.x).pow(2) + (oldP4.y - p3.y).pow(2))

        // 计算新的Line2终点，保持相同长度和平行方向
        val newLength = sqrt(dirX * dirX + dirY * dirY)
        if (newLength > 0) {
            val unitX = dirX / newLength
            val unitY = dirY / newLength

            val newP4 = PointF(
                p3.x + unitX * oldLength,
                p3.y + unitY * oldLength
            )

            viewPoints[3] = newP4
        }
    }

    /**
     * 🔧 将点投影到平行线上
     */
    private fun projectToParallelLine(touchPoint: PointF): PointF {
        if (viewPoints.size < 3) return touchPoint

        val p1 = viewPoints[0] // Line1起点
        val p2 = viewPoints[1] // Line1终点
        val p3 = viewPoints[2] // Line2起点

        // Line1的方向向量
        val dirX = p2.x - p1.x
        val dirY = p2.y - p1.y

        // 从Line2起点到触摸点的向量
        val toPointX = touchPoint.x - p3.x
        val toPointY = touchPoint.y - p3.y

        // 计算在方向向量上的投影
        val dotProduct = toPointX * dirX + toPointY * dirY
        val dirLengthSq = dirX * dirX + dirY * dirY

        if (dirLengthSq == 0f) return touchPoint

        val t = dotProduct / dirLengthSq

        // 投影点
        return PointF(p3.x + t * dirX, p3.y + t * dirY)
    }

    /**
     * 🔄 同步位图坐标（当需要保存时调用）
     */
    fun syncBitmapCoords(imageView: TpImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
    }

    /**
     * 🔄 从位图坐标恢复视图坐标（当缩放变化时调用）
     */
    fun syncViewCoords(imageView: TpImageView) {
        if (bitmapPoints.size == viewPoints.size) {
            viewPoints.clear()
            bitmapPoints.forEach { bitmapPoint ->
                val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
                viewPoints.add(viewPoint)
            }
        }
    }

    /**
     * 🎯 视图坐标转位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()

        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }

    /**
     * 🎯 位图坐标转视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }

    /**
     * 🎯 更新文本位置
     */
    fun updateTextPosition() {
        if (viewPoints.size >= 3) {
            val thirdPoint = viewPoints[2] // 第二条线的起点（第三个点）

            // 设置文本位置在第三个点的右上方
            var textX = thirdPoint.x + 40f // 向右偏移40像素
            var textY = thirdPoint.y - 30f // 向上偏移30像素

            // 简单的边界检测，确保文本不会超出视图边界
            if (textX < 50f) textX = 50f // 防止超出左边界
            if (textY < 50f) textY = 50f // 防止超出上边界

            textPosition = PointF(textX, textY)
        }
    }

    fun markAsModified() {
        lastModified = System.currentTimeMillis()
    }

    /**
     * 获取距离的显示文本（真实距离，不受缩放影响）
     */
    fun getDisplayText(): String {
        val distance = calculateDistance()
        return String.format("平行线距离: %.1f px", distance)
    }
}



/**
 * 📏 平行线测量助手 - 核心管理类
 */
class ParallelLinesMeasureHelper {
    
    companion object {
        private const val TAG = "ParallelLinesMeasureHelper"
        private const val TOUCH_RADIUS = 80f           // 触摸检测半径 - 与LineMeasureHelper保持一致
        private const val CLICK_TOLERANCE = 20f        // 点击容差
        private const val LONG_PRESS_DURATION = 800L   // 长按删除时间 - 与LineMeasureHelper保持一致
        private const val DEFAULT_LINE_LENGTH = 200f   // 默认线段长度
        private const val DEFAULT_LINE_SPACING = 100f  // 默认平行线间距
    }

    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var originalBitmap: Bitmap
    private var isInitialized = false

    // 测量数据管理 - 与其他Helper保持一致的结构
    private val measurements = mutableListOf<ParallelLinesMeasurement>()
    private var selectedMeasurement: ParallelLinesMeasurement? = null
    private var activeMeasurement: ParallelLinesMeasurement? = null

    // 交互状态管理 - 与其他Helper保持一致
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 回调函数
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🚀 初始化助手 - 与其他Helper保持一致的初始化模式
     */
    fun init(imageView: TpImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.originalBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        this.isInitialized = true

        // 清空之前的数据 - 与其他Helper保持一致
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()

        Log.d(TAG, "🚀 ParallelLinesMeasureHelper initialized")
    }

    /**
     * 🔄 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
        lastTouchX = 0f
        lastTouchY = 0f
    }

    /**
     * 📏 开始新的平行线测量 - 在屏幕中心创建默认平行线
     */
    fun startNewMeasurement(): String {
        if (!isInitialized) {
            Log.e(TAG, "❌ Helper not initialized")
            return ""
        }

        try {
            Log.d(TAG, "📏 Creating new parallel lines measurement")

            // 计算屏幕中心位置
            val centerX = imageView.width / 2f
            val centerY = imageView.height / 2f

            // 创建平行线测量实例
            val measurement = ParallelLinesMeasurement().apply {
                // 设置视图坐标：两条平行线，每条线两个端点
                // Line1: 水平线，稍微向上偏移
                viewPoints.add(PointF(centerX - DEFAULT_LINE_LENGTH / 2, centerY - DEFAULT_LINE_SPACING / 2))
                viewPoints.add(PointF(centerX + DEFAULT_LINE_LENGTH / 2, centerY - DEFAULT_LINE_SPACING / 2))

                // Line2: 平行线，稍微向下偏移
                viewPoints.add(PointF(centerX - DEFAULT_LINE_LENGTH / 2, centerY + DEFAULT_LINE_SPACING / 2))
                viewPoints.add(PointF(centerX + DEFAULT_LINE_LENGTH / 2, centerY + DEFAULT_LINE_SPACING / 2))

                isCompleted = true
                isSelected = true // 新创建的测量默认选中
                updateTextPosition()
            }

            // 同步位图坐标
            measurement.syncBitmapCoords(imageView)

            // 取消其他测量的选中状态 - 与LineMeasureHelper保持一致
            measurements.forEach {
                it.isSelected = false
                it.isEditing = false
            }

            // 添加到测量列表
            measurements.add(measurement)

            Log.d(TAG, "✅ New parallel lines measurement created: ${measurement.id}")
            Log.d(TAG, "📊 Current measurements count: ${measurements.size}")

            // 触发更新回调
            measurementUpdateCallback?.invoke()

            return measurement.id

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to create new parallel lines measurement: ${e.message}")
            e.printStackTrace()
            return ""
        }
    }

    /**
     * 📏 添加新的平行线测量（在测量模式下）
     */
    fun addNewMeasurement(): String {
        return startNewMeasurement()
    }

    /**
     * 🎯 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🎯 处理触摸事件 - 与LineMeasureHelper保持一致的交互逻辑
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Helper not initialized, ignoring touch event")
            return false
        }

        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> return handleTouchDown(touchPoint)
            MotionEvent.ACTION_MOVE -> return handleTouchMove(touchPoint)
            MotionEvent.ACTION_UP -> return handleTouchUp(touchPoint)
        }

        return false
    }

    private fun handleTouchDown(touchPoint: PointF): Boolean {
        // 1. 记录触摸起始信息
        longPressStartTime = System.currentTimeMillis()
        lastTouchX = touchPoint.x
        lastTouchY = touchPoint.y

        // 2. 检查端点触摸（优先级最高）
        for (measurement in measurements.reversed()) { // 从最新的开始检查
            for (i in measurement.viewPoints.indices) {
                if (measurement.isPointInTouchRange(touchPoint, i, TOUCH_RADIUS)) {
                    selectedMeasurement = measurement
                    draggedPointIndex = i
                    isDraggingPoint = true

                    // 3. 设置选中状态（不设置编辑状态）
                    measurements.forEach { it.isSelected = false }
                    measurement.isSelected = true

                    measurementUpdateCallback?.invoke()
                    Log.d(TAG, "🎯 Started dragging point $i of measurement ${measurement.id}")
                    return true
                }
            }
        }

        // 🔧 智能空白区域检测 - 避免UI按钮区域被误判 - 与LineMeasureHelper保持一致
        if (isInImageContentArea(touchPoint, imageView.width, imageView.height)) {
            measurements.forEach { it.isSelected = false }
            selectedMeasurement = null
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "� Cleared all selections - empty area clicked in image content")
        } else {
            Log.d(TAG, "🛡️ Clicked in UI area, preserving selection state")
        }

        return false // 让ImageView处理缩放
    }

    private fun handleTouchMove(touchPoint: PointF): Boolean {
        if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex >= 0) {
            // 正常拖拽处理
            selectedMeasurement!!.updateWithParallelConstraint(draggedPointIndex, touchPoint)
            selectedMeasurement!!.syncBitmapCoords(imageView)
            measurementUpdateCallback?.invoke()
            return true
        } else if (!isDraggingPoint && selectedMeasurement != null) {
            // 触摸恢复机制
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, measurement.getNearestPointIndex(touchPoint), TOUCH_RADIUS) && measurement == selectedMeasurement) {
                    draggedPointIndex = measurement.getNearestPointIndex(touchPoint)
                    isDraggingPoint = true
                    // 立即处理这次移动
                    selectedMeasurement!!.updateWithParallelConstraint(draggedPointIndex, touchPoint)
                    selectedMeasurement!!.syncBitmapCoords(imageView)
                    measurementUpdateCallback?.invoke()
                    Log.d(TAG, "🔄 Touch recovery successful - resumed dragging point $draggedPointIndex")
                    return true
                }
            }
        }
        return false
    }

    private fun handleTouchUp(touchPoint: PointF): Boolean {
        val touchDuration = System.currentTimeMillis() - longPressStartTime
        val touchDistance = sqrt((touchPoint.x - lastTouchX) * (touchPoint.x - lastTouchX) + (touchPoint.y - lastTouchY) * (touchPoint.y - lastTouchY))
        val wasDragging = isDraggingPoint

        var handled = false

        // 重置拖拽状态
        isDraggingPoint = false
        draggedPointIndex = -1

        if (wasDragging) {
            // 拖拽完成
            measurementUpdateCallback?.invoke()
            handled = true
        }

        // 长按删除 - 与LineMeasureHelper保持一致
        if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                    measurements.remove(measurement)
                    if (selectedMeasurement == measurement) {
                        selectedMeasurement = null
                    }
                    measurementUpdateCallback?.invoke()
                    Log.d(TAG, "🗑️ Long-press: Deleted measurement ${measurement.id}")
                    handled = true
                    break
                }
            }
        }

        // 轻触选中 - 与LineMeasureHelper保持一致
        if (!wasDragging && !handled && touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                    measurements.forEach { it.isSelected = false }
                    measurement.isSelected = true
                    selectedMeasurement = measurement
                    measurementUpdateCallback?.invoke()
                    Log.d(TAG, "🎯 Light touch selection - selected measurement ${measurement.id}")
                    return true
                }
            }
        }

        return handled
    }

    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域） - 与LineMeasureHelper保持一致
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in isInImageContentArea: ${e.message}")
            // 出错时保守处理，不清除选中状态
            return false
        }
    }

    /**
     * 📊 获取所有测量数据（用于渲染）
     */
    fun getAllMeasurementData(): List<ParallelLinesMeasurementData> {
        return measurements.map { measurement ->
            ParallelLinesMeasurementData(
                id = measurement.id,
                viewPoints = measurement.viewPoints.toList(),
                bitmapPoints = measurement.bitmapPoints.toList(),
                isSelected = measurement.isSelected,
                isEditing = measurement.isEditing,
                isCompleted = measurement.isCompleted,
                textPosition = measurement.textPosition,
                distance = measurement.calculateDistance()
            )
        }
    }

    /**
     * 🔄 缩放变化时同步坐标 - 与其他Helper保持一致
     */
    fun onScaleChanged() {
        measurements.forEach { measurement ->
            measurement.syncViewCoords(imageView)
            measurement.updateTextPosition() // 更新文本位置
        }
        measurementUpdateCallback?.invoke()
    }

    /**
     * 🎯 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 🎯 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean {
        return measurements.any { it.isSelected }
    }

    /**
     * 🎯 检查触摸点是否靠近任何测量 - 与其他Helper保持一致
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.viewPoints.any { point ->
                val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
                distance <= TOUCH_RADIUS
            }
        }
    }

    /**
     * 🎯 检查触摸点是否在任何测量上（用于智能模式激活）
     */
    fun isPointOnMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.isPointInTouchRange(touchPoint, measurement.getNearestPointIndex(touchPoint), TOUCH_RADIUS)
        }
    }

    /**
     * 📊 获取所有测量
     */
    fun getAllMeasurements(): List<ParallelLinesMeasurement> = measurements.toList()

    /**
     * 🔄 设置选中的测量
     */
    fun setSelectedMeasurement(measurement: ParallelLinesMeasurement) {
        // 确保测量在列表中
        if (measurements.contains(measurement)) {
            // 清除其他测量的选中状态
            measurements.forEach { it.isSelected = false }
            // 设置新的选中测量
            measurement.isSelected = true
            selectedMeasurement = measurement
            Log.d(TAG, "🔄 Selected measurement set: ${measurement.id}")
            measurementUpdateCallback?.invoke()
        } else {
            Log.w(TAG, "⚠️ Cannot select measurement not in list: ${measurement.id}")
        }
    }

    /**
     * 🔄 清除选中状态 - 与其他Helper保持一致
     */
    fun clearSelection() {
        Log.d(TAG, "🔄 Clearing all selections - current selectedMeasurement: ${selectedMeasurement?.id}")
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🔄 Cleared all parallel lines selections")
    }

    /**
     * 🗑️ 删除选中的测量 - 与LineMeasureHelper保持一致
     */
    fun deleteSelectedMeasurement(): Boolean {
        val selectedIndex = measurements.indexOfFirst { it.isSelected }
        return if (selectedIndex >= 0) {
            val deletedMeasurement = measurements.removeAt(selectedIndex)
            selectedMeasurement = null
            activeMeasurement = null
            resetInteractionState()
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "🗑️ Deleted selected parallel lines measurement at index $selectedIndex: ${deletedMeasurement.id}")
            true
        } else {
            Log.w(TAG, "⚠️ No selected parallel lines measurement to delete")
            false
        }
    }

    /**
     * 🧹 清理所有测量数据
     */
    fun clearAllMeasurements() {
        Log.d(TAG, "🧹 Clearing all parallel lines measurements")
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
        measurementUpdateCallback?.invoke()
    }

    /**
     * ⏸️ 暂停测量 - 与其他Helper保持一致
     */
    fun pauseMeasurement() {
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
    }

    /**
     * ▶️ 恢复测量 - 与其他Helper保持一致
     */
    fun resumeMeasurement() {
        // 恢复时不需要特殊操作，保持当前状态
    }

    /**
     * 🧹 重置助手 - 与其他Helper保持一致
     */
    fun reset() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        resetInteractionState()
    }
}
